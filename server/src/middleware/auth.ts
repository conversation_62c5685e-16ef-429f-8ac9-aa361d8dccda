import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { User } from '@/models/User';
import { config } from '@/config/environment';
import { CustomError, asyncHandler } from './errorHandler';
import { HTTP_STATUS, ERROR_MESSAGES } from '@portfolio/shared';

export interface AuthRequest extends Request {
  user?: any;
}

export const authenticate = asyncHandler(async (req: AuthRequest, _res: Response, next: NextFunction) => {
  let token;

  // Get token from header
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }
  // Get token from cookie
  else if (req.cookies.token) {
    token = req.cookies.token;
  }

  if (!token) {
    throw new CustomError(ERROR_MESSAGES.UNAUTHORIZED, HTTP_STATUS.UNAUTHORIZED);
  }

  try {
    // Verify token
    const decoded = jwt.verify(token, config.jwtSecret) as any;

    // Check if user still exists
    const user = await User.findById(decoded.id).select('-password');
    if (!user) {
      throw new CustomError('The user belonging to this token does no longer exist.', HTTP_STATUS.UNAUTHORIZED);
    }

    // Check if user is active
    if (!user.isActive) {
      throw new CustomError('Your account has been deactivated. Please contact support.', HTTP_STATUS.UNAUTHORIZED);
    }

    // Grant access to protected route
    req.user = user;
    next();
  } catch (error) {
    throw new CustomError(ERROR_MESSAGES.UNAUTHORIZED, HTTP_STATUS.UNAUTHORIZED);
  }
});

export const authorize = (...roles: string[]) => {
  return (req: AuthRequest, next: NextFunction) => {
    if (!req.user) {
      throw new CustomError(ERROR_MESSAGES.UNAUTHORIZED, HTTP_STATUS.UNAUTHORIZED);
    }

    if (!roles.includes(req.user.role)) {
      throw new CustomError(ERROR_MESSAGES.FORBIDDEN, HTTP_STATUS.FORBIDDEN);
    }

    next();
  };
};

export const optionalAuth = asyncHandler(async (req: AuthRequest, next: NextFunction) => {
  let token;

  // Get token from header
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }
  // Get token from cookie
  else if (req.cookies.token) {
    token = req.cookies.token;
  }

  if (token) {
    try {
      // Verify token
      const decoded = jwt.verify(token, config.jwtSecret) as any;

      // Check if user still exists
      const user = await User.findById(decoded.id).select('-password');
      if (user && user.isActive) {
        req.user = user;
      }
    } catch (error) {
      // Token is invalid, but that's okay for optional auth
      // Just continue without setting req.user
    }
  }

  next();
});
