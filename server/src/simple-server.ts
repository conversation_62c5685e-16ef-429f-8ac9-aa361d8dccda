import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import cookieParser from 'cookie-parser';
import rateLimit from 'express-rate-limit';

const app = express();
const PORT = parseInt(process.env.PORT || '5000', 10);
const HOST = process.env.HOST || 'localhost';

// Trust proxy for rate limiting and IP detection
app.set('trust proxy', 1);

// Security middleware
app.use(helmet({
  contentSecurityPolicy: false,
  crossOriginEmbedderPolicy: false,
}));

// CORS configuration
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests from this IP, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use('/api/', limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(cookieParser());

// Compression
app.use(compression());

// Logging
app.use(morgan('combined'));

// Health check endpoint
app.get('/health', (_req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
  });
});

// Mock projects endpoint
app.get('/api/projects', (_req, res) => {
  const mockProjects = [
    {
      id: '1',
      title: 'HealthCare Chatbot',
      description: 'Building a 24x7 Healthcare Chatbot using NLP 24x7 support to patients',
      image: '/assets/healthcare.jpg',
      technologies: ['NLP', 'AI', 'Python'],
      githubUrl: 'https://github.com/thestarsahil/Mentalmate',
      featured: true,
      category: 'ai-ml',
      status: 'completed',
    },
    {
      id: '2',
      title: 'Weather WebApp',
      description: 'Web Application using HTML CSS JS with the help of Open Weather API',
      image: '/assets/weather.png',
      technologies: ['HTML', 'CSS', 'JS', 'API'],
      featured: false,
      category: 'web-development',
      status: 'completed',
    },
    {
      id: '3',
      title: 'Contoso Real Estate',
      description: 'Microsoft Real Estate Project allows users to listed properties for sale or rent',
      image: '/assets/real-estate.jpg',
      technologies: ['C#', '.NET', 'Azure'],
      featured: true,
      category: 'web-development',
      status: 'completed',
    },
    {
      id: '4',
      title: 'CropForesight',
      description: 'Assist farmers making smart choices about which crops to grow on their land',
      image: '/assets/crop.jpg',
      technologies: ['ML', 'Python', 'Data'],
      featured: false,
      category: 'ai-ml',
      status: 'completed',
    },
    {
      id: '5',
      title: 'Book Finder',
      description: 'Real-time project that helps users find and purchase books online FREE',
      image: '/assets/book-finder.jpg',
      technologies: ['React', 'Node', 'MongoDB'],
      featured: true,
      category: 'web-development',
      status: 'completed',
    },
  ];

  res.status(200).json({
    success: true,
    data: mockProjects,
    timestamp: new Date().toISOString(),
  });
});

// Mock skills endpoint
app.get('/api/skills', (_req, res) => {
  const mockSkills = [
    {
      id: '1',
      name: 'Prompt Engineering',
      category: 'ai-ml',
      proficiency: 62,
      icon: 'terminal',
      description: 'Advanced prompt engineering for AI models',
    },
    {
      id: '2',
      name: 'AI Model Development',
      category: 'ai-ml',
      proficiency: 31,
      icon: 'bot',
      description: 'Building and training AI models',
    },
    {
      id: '3',
      name: 'C++ Programming',
      category: 'programming-languages',
      proficiency: 65,
      icon: 'code',
      description: 'Embedded systems and performance-critical applications',
    },
    {
      id: '4',
      name: 'Server & Database Management',
      category: 'databases',
      proficiency: 62,
      icon: 'database',
      description: 'Database design and server administration',
    },
    {
      id: '5',
      name: 'Data Structures & Algorithms',
      category: 'programming-languages',
      proficiency: 53,
      icon: 'cpu',
      description: 'Competitive programming and algorithm optimization',
    },
  ];

  res.status(200).json({
    success: true,
    data: mockSkills,
    timestamp: new Date().toISOString(),
  });
});

// Mock contact endpoint
app.post('/api/contact', (req, res) => {
  const { name, email, subject, message } = req.body;
  
  console.log('Contact form submission:', { name, email, subject, message });
  
  // Simulate processing delay
  setTimeout(() => {
    res.status(201).json({
      success: true,
      message: 'Message transmitted securely!',
      data: {
        id: Date.now().toString(),
        name,
        email,
        subject,
        createdAt: new Date().toISOString(),
      },
      timestamp: new Date().toISOString(),
    });
  }, 1500);
});

// 404 handler
app.use((_req, res) => {
  res.status(404).json({
    success: false,
    error: 'Route not found',
    timestamp: new Date().toISOString(),
  });
});

// Error handler
app.use((err: any, _req: any, res: any, _next: any) => {
  console.error('Error:', err);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    timestamp: new Date().toISOString(),
  });
});

// Start server
const server = app.listen(PORT, HOST, () => {
  console.log(`🚀 Server running on http://${HOST}:${PORT}`);
  console.log(`📊 Health check: http://${HOST}:${PORT}/health`);
  console.log(`🔗 API endpoints: http://${HOST}:${PORT}/api/*`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received. Shutting down gracefully...');
  server.close(() => {
    console.log('Server closed.');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received. Shutting down gracefully...');
  server.close(() => {
    console.log('Server closed.');
    process.exit(0);
  });
});

export { app };
