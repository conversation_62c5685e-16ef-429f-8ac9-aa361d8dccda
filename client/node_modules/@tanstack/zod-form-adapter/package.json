{"name": "@tanstack/zod-form-adapter", "version": "0.42.1", "description": "The Zod adapter for TanStack Form.", "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/TanStack/form.git", "directory": "packages/zod-form-adapter"}, "homepage": "https://tanstack.com/form", "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}, "type": "module", "types": "dist/esm/index.d.ts", "main": "dist/cjs/index.cjs", "module": "dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "sideEffects": false, "files": ["dist", "src"], "dependencies": {"@tanstack/form-core": "0.42.1"}, "devDependencies": {"zod": "^3.24.0", "@tanstack/react-form": "0.42.1"}, "peerDependencies": {"zod": "^3.x"}, "scripts": {}}