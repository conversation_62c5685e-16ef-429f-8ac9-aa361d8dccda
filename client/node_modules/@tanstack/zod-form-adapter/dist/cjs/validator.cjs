"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
function prefixSchemaToErrors(zodErrors, transformErrors) {
  const schema = /* @__PURE__ */ new Map();
  for (const zodError of zodErrors) {
    const path = zodError.path.map(
      (segment) => typeof segment === "number" ? `[${segment}]` : segment
    ).join(".").replace(/\.\[/g, "[");
    schema.set(path, (schema.get(path) ?? []).concat(zodError));
  }
  const transformedSchema = {};
  schema.forEach((value, key) => {
    transformedSchema[key] = transformErrors(value);
  });
  return transformedSchema;
}
function defaultFormTransformer(transformErrors) {
  return (zodErrors) => ({
    form: transformErrors(zodErrors),
    fields: prefixSchemaToErrors(zodErrors, transformErrors)
  });
}
const zodValidator = (params = {}) => () => {
  const transformFieldErrors = params.transformErrors ?? ((issues) => issues.map((issue) => issue.message).join(", "));
  const getTransformStrategy = (validationSource) => validationSource === "form" ? defaultFormTransformer(transformFieldErrors) : transformFieldErrors;
  return {
    validate({ value, validationSource }, fn) {
      const result = fn.safeParse(value);
      if (result.success) return;
      const transformer = getTransformStrategy(validationSource);
      return transformer(result.error.issues);
    },
    async validateAsync({ value, validationSource }, fn) {
      const result = await fn.safeParseAsync(value);
      if (result.success) return;
      const transformer = getTransformStrategy(validationSource);
      return transformer(result.error.issues);
    }
  };
};
exports.defaultFormTransformer = defaultFormTransformer;
exports.prefixSchemaToErrors = prefixSchemaToErrors;
exports.zodValidator = zodValidator;
//# sourceMappingURL=validator.cjs.map
