{"version": 3, "file": "validator.cjs", "sources": ["../../src/validator.ts"], "sourcesContent": ["import type {\n  ValidationError,\n  Validator,\n  ValidatorAdapterParams,\n} from '@tanstack/form-core'\nimport type { ZodIssue, ZodType } from 'zod'\n\ntype Params = ValidatorAdapterParams<ZodIssue>\ntype TransformFn = NonNullable<Params['transformErrors']>\n\nexport function prefixSchemaToErrors(\n  zodErrors: ZodIssue[],\n  transformErrors: TransformFn,\n) {\n  const schema = new Map<string, ZodIssue[]>()\n\n  for (const zodError of zodErrors) {\n    const path = zodError.path\n      .map((segment) =>\n        typeof segment === 'number' ? `[${segment}]` : segment,\n      )\n      .join('.')\n      .replace(/\\.\\[/g, '[')\n    schema.set(path, (schema.get(path) ?? []).concat(zodError))\n  }\n\n  const transformedSchema = {} as Record<string, ValidationError>\n\n  schema.forEach((value, key) => {\n    transformedSchema[key] = transformErrors(value)\n  })\n\n  return transformedSchema\n}\n\nexport function defaultFormTransformer(transformErrors: TransformFn) {\n  return (zodErrors: ZodIssue[]) => ({\n    form: transformErrors(zodErrors),\n    fields: prefixSchemaToErrors(zodErrors, transformErrors),\n  })\n}\n\n/**\n * @deprecated With zod 3.24.0 the adapter is no longer needed and will be soon removed.\n * If you were passing some parameters you can use the `standardSchemaValidator` instead.\n */\nexport const zodValidator =\n  (params: Params = {}): Validator<unknown, ZodType> =>\n  () => {\n    const transformFieldErrors =\n      params.transformErrors ??\n      ((issues: ZodIssue[]) => issues.map((issue) => issue.message).join(', '))\n\n    const getTransformStrategy = (validationSource: 'form' | 'field') =>\n      validationSource === 'form'\n        ? defaultFormTransformer(transformFieldErrors)\n        : transformFieldErrors\n\n    return {\n      validate({ value, validationSource }, fn) {\n        const result = fn.safeParse(value)\n        if (result.success) return\n\n        const transformer = getTransformStrategy(validationSource)\n\n        return transformer(result.error.issues)\n      },\n      async validateAsync({ value, validationSource }, fn) {\n        const result = await fn.safeParseAsync(value)\n        if (result.success) return\n\n        const transformer = getTransformStrategy(validationSource)\n\n        return transformer(result.error.issues)\n      },\n    }\n  }\n"], "names": [], "mappings": ";;AAUgB,SAAA,qBACd,WACA,iBACA;AACM,QAAA,6BAAa,IAAwB;AAE3C,aAAW,YAAY,WAAW;AAC1B,UAAA,OAAO,SAAS,KACnB;AAAA,MAAI,CAAC,YACJ,OAAO,YAAY,WAAW,IAAI,OAAO,MAAM;AAAA,MAEhD,KAAK,GAAG,EACR,QAAQ,SAAS,GAAG;AAChB,WAAA,IAAI,OAAO,OAAO,IAAI,IAAI,KAAK,CAAC,GAAG,OAAO,QAAQ,CAAC;AAAA,EAAA;AAG5D,QAAM,oBAAoB,CAAC;AAEpB,SAAA,QAAQ,CAAC,OAAO,QAAQ;AACX,sBAAA,GAAG,IAAI,gBAAgB,KAAK;AAAA,EAAA,CAC/C;AAEM,SAAA;AACT;AAEO,SAAS,uBAAuB,iBAA8B;AACnE,SAAO,CAAC,eAA2B;AAAA,IACjC,MAAM,gBAAgB,SAAS;AAAA,IAC/B,QAAQ,qBAAqB,WAAW,eAAe;AAAA,EAAA;AAE3D;AAMO,MAAM,eACX,CAAC,SAAiB,CAAA,MAClB,MAAM;AACJ,QAAM,uBACJ,OAAO,oBACN,CAAC,WAAuB,OAAO,IAAI,CAAC,UAAU,MAAM,OAAO,EAAE,KAAK,IAAI;AAEzE,QAAM,uBAAuB,CAAC,qBAC5B,qBAAqB,SACjB,uBAAuB,oBAAoB,IAC3C;AAEC,SAAA;AAAA,IACL,SAAS,EAAE,OAAO,iBAAA,GAAoB,IAAI;AAClC,YAAA,SAAS,GAAG,UAAU,KAAK;AACjC,UAAI,OAAO,QAAS;AAEd,YAAA,cAAc,qBAAqB,gBAAgB;AAElD,aAAA,YAAY,OAAO,MAAM,MAAM;AAAA,IACxC;AAAA,IACA,MAAM,cAAc,EAAE,OAAO,iBAAA,GAAoB,IAAI;AACnD,YAAM,SAAS,MAAM,GAAG,eAAe,KAAK;AAC5C,UAAI,OAAO,QAAS;AAEd,YAAA,cAAc,qBAAqB,gBAAgB;AAElD,aAAA,YAAY,OAAO,MAAM,MAAM;AAAA,IAAA;AAAA,EAE1C;AACF;;;;"}