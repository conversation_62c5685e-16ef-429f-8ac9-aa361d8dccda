import { ValidationError, Validator, ValidatorAdapterParams } from '@tanstack/form-core';
import { ZodIssue, ZodType } from 'zod';
type Params = ValidatorAdapterParams<ZodIssue>;
type TransformFn = NonNullable<Params['transformErrors']>;
export declare function prefixSchemaToErrors(zodErrors: ZodIssue[], transformErrors: TransformFn): Record<string, ValidationError>;
export declare function defaultFormTransformer(transformErrors: TransformFn): (zodErrors: ZodIssue[]) => {
    form: ValidationError;
    fields: Record<string, ValidationError>;
};
/**
 * @deprecated With zod 3.24.0 the adapter is no longer needed and will be soon removed.
 * If you were passing some parameters you can use the `standardSchemaValidator` instead.
 */
export declare const zodValidator: (params?: Params) => Validator<unknown, ZodType>;
export {};
