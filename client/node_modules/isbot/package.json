{"name": "isbot", "version": "5.1.28", "description": "🤖/👨‍🦰 Recognise bots/crawlers/spiders using the user agent string.", "keywords": ["bot", "crawlers", "spiders", "googlebot", "useragent", "user agent parsing", "🤖"], "license": "Unlicense", "homepage": "https://isbot.js.org", "repository": {"type": "git", "url": "https://github.com/omrilotan/isbot"}, "engines": {"node": ">=18"}, "files": ["./index*"], "type": "commonjs", "main": "./index.js", "module": "./index.mjs", "browser": "./index.mjs", "jsdelivr": "./index.iife.js", "exports": {"./package.json": "./package.json", ".": {"browser": {"import": "./index.mjs", "require": "./index.js"}, "node": {"import": "./index.mjs", "require": "./index.js"}, "import": "./index.mjs", "require": "./index.js", "default": "./index.js"}}, "sideEffects": false, "types": "index.d.ts", "scripts": {"prepare": "./scripts/prepare/index.js", "build": "./scripts/build/procedure.sh", "format": "./scripts/format/procedure.sh", "pretest": "npm run build && npm run prepare", "test": "./scripts/test/procedure.sh", "prepublishOnly": "./scripts/prepublish/procedure.sh", "prestart": "which parcel || npm i parcel-bundler --no-save", "start": "parcel page/index.pug --out-dir docs", "prepage": "which parcel || npm i parcel-bundler --no-save", "page": "parcel build page/index.pug --out-dir docs --public-url ./"}, "devDependencies": {"@types/jest": "^29.5.12", "esbuild": "^0.25.0", "jest": "^29.7.0", "prettier": "^3.2.5", "pug": "^3.0.2", "stdline": "^1.1.1", "ts-jest": "^29.1.1", "typescript": "^5.3.3", "user-agents": "^1.1.116", "yaml": "^2.3.4"}}