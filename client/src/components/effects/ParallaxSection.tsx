'use client';

import React, { ReactNode } from 'react';
import { motion } from 'framer-motion';
import { useScrollAnimation } from '@/hooks/useParallax';
import { FloatingElement } from './ParallaxContainer';

interface ParallaxSectionProps {
  children: ReactNode;
  id?: string;
  className?: string;
  backgroundElements?: ReactNode[];
  floatingElements?: Array<{
    content: ReactNode;
    position: string;
    speed?: number;
    direction?: 'up' | 'down' | 'left' | 'right';
    delay?: number;
  }>;
}

export function ParallaxSection({
  children,
  id,
  className = '',
  backgroundElements = [],
  floatingElements = []
}: ParallaxSectionProps) {
  const scrollAnimation = useScrollAnimation(0.1);

  return (
    <section
      id={id}
      ref={scrollAnimation.ref}
      className={`relative overflow-hidden ${className}`}
    >
      {/* Background parallax elements */}
      {backgroundElements.map((element, index) => (
        <div
          key={`bg-${index}`}
          className="absolute inset-0 pointer-events-none"
          style={{ zIndex: -1 }}
        >
          {element}
        </div>
      ))}

      {/* Floating elements */}
      {floatingElements.map((element, index) => (
        <FloatingElement
          key={`float-${index}`}
          className={element.position}
          speed={element.speed}
          direction={element.direction}
          delay={element.delay}
        >
          {element.content}
        </FloatingElement>
      ))}

      {/* Main content with staggered animation */}
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={scrollAnimation.isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
        transition={{
          duration: 0.8,
          ease: 'easeOut',
          staggerChildren: 0.1
        }}
        className="relative z-10"
      >
        {children}
      </motion.div>
    </section>
  );
}

// Specialized sections for different content types
export function HeroParallaxSection({
  children,
  className = ''
}: {
  children: ReactNode;
  className?: string;
}) {
  const floatingElements = [
    {
      content: (
        <div className="text-darkweb-green font-mono text-sm opacity-30 animate-float">
          {'> Initializing...'}
        </div>
      ),
      position: 'top-1/4 left-10',
      speed: 0.3,
      direction: 'up' as const,
      delay: 0.5
    },
    {
      content: (
        <div className="text-darkweb-purple font-mono text-sm opacity-30 animate-float">
          {'[SECURE CONNECTION]'}
        </div>
      ),
      position: 'top-1/3 right-10',
      speed: 0.4,
      direction: 'down' as const,
      delay: 0.8
    },
    {
      content: (
        <div className="text-darkweb-accent font-mono text-sm opacity-30 animate-float">
          {'$ sudo access_granted'}
        </div>
      ),
      position: 'bottom-1/4 left-1/4',
      speed: 0.5,
      direction: 'left' as const,
      delay: 1.2
    },
    {
      content: (
        <div className="text-darkweb-text font-mono text-xs opacity-20">
          {'[ENCRYPTED]'}
        </div>
      ),
      position: 'top-1/2 right-1/4',
      speed: 0.2,
      direction: 'right' as const,
      delay: 1.5
    }
  ];

  return (
    <ParallaxSection
      id="home"
      className={`min-h-screen flex items-center justify-center section-padding ${className}`}
      floatingElements={floatingElements}
    >
      {children}
    </ParallaxSection>
  );
}

export function ContentParallaxSection({
  children,
  id,
  className = '',
  addFloatingCode = true,
  backgroundElements = []
}: {
  children: ReactNode;
  id?: string;
  className?: string;
  addFloatingCode?: boolean;
  backgroundElements?: ReactNode[];
}) {
  const floatingElements = addFloatingCode ? [
    {
      content: (
        <div className="text-darkweb-green font-mono text-xs opacity-10 rotate-12">
          {'</>'}
        </div>
      ),
      position: 'top-20 right-20',
      speed: 0.3,
      direction: 'up' as const,
      delay: 0.3
    },
    {
      content: (
        <div className="text-darkweb-purple font-mono text-xs opacity-10 -rotate-12">
          {'{ }'}
        </div>
      ),
      position: 'bottom-20 left-20',
      speed: 0.4,
      direction: 'down' as const,
      delay: 0.6
    }
  ] : [];

  return (
    <ParallaxSection
      id={id}
      className={`section-padding ${className}`}
      backgroundElements={backgroundElements}
      floatingElements={floatingElements}
    >
      {children}
    </ParallaxSection>
  );
}
