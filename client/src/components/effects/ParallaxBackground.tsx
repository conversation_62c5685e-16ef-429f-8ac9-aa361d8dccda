'use client';

import React from 'react';
import { useParallax } from '@/hooks/useParallax';

interface ParallaxBackgroundProps {
  className?: string;
  speed?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
  children?: React.ReactNode;
}

export function ParallaxBackground({
  className = '',
  speed = 0.5,
  direction = 'up',
  children
}: ParallaxBackgroundProps) {
  const parallax = useParallax({ speed, direction });

  return (
    <div
      ref={parallax.ref as React.RefObject<HTMLDivElement>}
      className={`absolute inset-0 pointer-events-none ${className}`}
      style={parallax.style}
    >
      {children}
    </div>
  );
}

// Animated grid background
export function AnimatedGridBackground({ 
  speed = 0.3,
  opacity = 0.1 
}: { 
  speed?: number;
  opacity?: number;
}) {
  return (
    <ParallaxBackground speed={speed} className="overflow-hidden">
      <div 
        className="absolute inset-0 bg-grid-pattern animate-grid-move"
        style={{ 
          opacity,
          backgroundImage: `
            linear-gradient(rgba(0, 255, 0, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0, 255, 0, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px'
        }}
      />
    </ParallaxBackground>
  );
}

// Floating particles background
export function FloatingParticlesBackground({
  speed = 0.4,
  particleCount = 20
}: {
  speed?: number;
  particleCount?: number;
}) {
  const particles = Array.from({ length: particleCount }, (_, i) => ({
    id: i,
    size: Math.random() * 4 + 2,
    x: Math.random() * 100,
    y: Math.random() * 100,
    delay: Math.random() * 5,
    duration: Math.random() * 10 + 10
  }));

  return (
    <ParallaxBackground speed={speed}>
      {particles.map((particle) => (
        <div
          key={particle.id}
          className="absolute w-1 h-1 bg-darkweb-accent rounded-full opacity-30 animate-float"
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            animationDelay: `${particle.delay}s`,
            animationDuration: `${particle.duration}s`
          }}
        />
      ))}
    </ParallaxBackground>
  );
}

// Code rain effect (Matrix-style)
export function CodeRainBackground({
  speed = 0.6,
  intensity = 0.05
}: {
  speed?: number;
  intensity?: number;
}) {
  const codeChars = ['0', '1', '{', '}', '<', '>', '/', '\\', '|', '-', '+', '='];
  const columns = 20;
  
  const rainColumns = Array.from({ length: columns }, (_, i) => ({
    id: i,
    chars: Array.from({ length: 15 }, () => 
      codeChars[Math.floor(Math.random() * codeChars.length)]
    ),
    delay: Math.random() * 5,
    duration: Math.random() * 3 + 2
  }));

  return (
    <ParallaxBackground speed={speed} className="font-mono text-xs">
      {rainColumns.map((column) => (
        <div
          key={column.id}
          className="absolute top-0 flex flex-col text-darkweb-green animate-code-rain"
          style={{
            left: `${(column.id / columns) * 100}%`,
            opacity: intensity,
            animationDelay: `${column.delay}s`,
            animationDuration: `${column.duration}s`
          }}
        >
          {column.chars.map((char, charIndex) => (
            <span
              key={charIndex}
              className="block leading-tight"
              style={{
                animationDelay: `${charIndex * 0.1}s`
              }}
            >
              {char}
            </span>
          ))}
        </div>
      ))}
    </ParallaxBackground>
  );
}

// Geometric shapes background
export function GeometricShapesBackground({
  speed = 0.3,
  shapeCount = 8
}: {
  speed?: number;
  shapeCount?: number;
}) {
  const shapes = Array.from({ length: shapeCount }, (_, i) => ({
    id: i,
    type: ['circle', 'square', 'triangle'][Math.floor(Math.random() * 3)],
    size: Math.random() * 100 + 50,
    x: Math.random() * 100,
    y: Math.random() * 100,
    rotation: Math.random() * 360,
    opacity: Math.random() * 0.1 + 0.05
  }));

  return (
    <ParallaxBackground speed={speed}>
      {shapes.map((shape) => (
        <div
          key={shape.id}
          className={`absolute border border-darkweb-accent animate-spin-slow ${
            shape.type === 'circle' ? 'rounded-full' : 
            shape.type === 'triangle' ? 'triangle' : ''
          }`}
          style={{
            left: `${shape.x}%`,
            top: `${shape.y}%`,
            width: `${shape.size}px`,
            height: `${shape.size}px`,
            transform: `rotate(${shape.rotation}deg)`,
            opacity: shape.opacity,
            animationDuration: '20s'
          }}
        />
      ))}
    </ParallaxBackground>
  );
}
