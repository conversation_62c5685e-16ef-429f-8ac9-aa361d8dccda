'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { ParallaxSection, HeroParallaxSection, ContentParallaxSection } from './ParallaxSection';
import { 
  ParallaxContainer, 
  ParallaxText, 
  ParallaxBackground, 
  FloatingElement 
} from './ParallaxContainer';
import { 
  AnimatedGridBackground, 
  FloatingParticlesBackground, 
  CodeRainBackground, 
  GeometricShapesBackground 
} from './ParallaxBackground';

/**
 * Demo component showcasing all parallax effects
 * This component demonstrates various parallax animations and effects
 * that can be used throughout the portfolio
 */
export function ParallaxDemo() {
  return (
    <div className="space-y-0">
      {/* Hero Section with Floating Elements */}
      <HeroParallaxSection className="bg-gradient-to-br from-darkweb-bg to-darkweb-surface/50">
        <div className="container-custom text-center">
          <ParallaxText speed={0.1} enableMouseParallax={true}>
            <motion.h1 
              className="text-5xl md:text-7xl font-bold text-darkweb-text mb-6"
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1 }}
            >
              Parallax <span className="text-darkweb-accent glitch-text" data-text="Effects">Effects</span>
            </motion.h1>
          </ParallaxText>
          
          <ParallaxContainer 
            parallaxOptions={{ speed: 0.2 }}
            enableMouseParallax={true}
            mouseIntensity={0.05}
          >
            <motion.p 
              className="text-xl text-darkweb-muted max-w-2xl mx-auto"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.3 }}
            >
              Experience smooth parallax scrolling with multiple layers and interactive elements
            </motion.p>
          </ParallaxContainer>
        </div>
      </HeroParallaxSection>

      {/* Grid Background Section */}
      <ContentParallaxSection 
        className="min-h-screen bg-darkweb-surface/20"
        backgroundElements={[<AnimatedGridBackground key="grid" speed={0.3} opacity={0.1} />]}
      >
        <div className="container-custom py-20">
          <ParallaxText speed={0.15}>
            <h2 className="text-4xl font-bold text-darkweb-text mb-8 text-center">
              Grid Background Effect
            </h2>
          </ParallaxText>
          
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <ParallaxContainer 
              parallaxOptions={{ speed: 0.2, direction: 'left' }}
              enableMouseParallax={true}
            >
              <div className="terminal-window">
                <div className="terminal-header">
                  <div className="terminal-buttons">
                    <span className="terminal-button close"></span>
                    <span className="terminal-button minimize"></span>
                    <span className="terminal-button maximize"></span>
                  </div>
                  <div className="terminal-title">parallax.config</div>
                </div>
                <div className="terminal-body">
                  <pre className="text-darkweb-green text-sm">
{`{
  "speed": 0.3,
  "direction": "up",
  "mouseParallax": true,
  "backgroundGrid": true
}`}
                  </pre>
                </div>
              </div>
            </ParallaxContainer>
            
            <ParallaxContainer 
              parallaxOptions={{ speed: 0.25, direction: 'right' }}
              enableMouseParallax={true}
            >
              <div className="space-y-4">
                <h3 className="text-2xl font-bold text-darkweb-accent">Features</h3>
                <ul className="space-y-2 text-darkweb-text">
                  <li>• Smooth scroll-based parallax</li>
                  <li>• Mouse movement parallax</li>
                  <li>• Animated background grids</li>
                  <li>• Performance optimized</li>
                  <li>• Mobile responsive</li>
                </ul>
              </div>
            </ParallaxContainer>
          </div>
        </div>
      </ContentParallaxSection>

      {/* Floating Particles Section */}
      <ContentParallaxSection 
        className="min-h-screen bg-darkweb-bg"
        backgroundElements={[<FloatingParticlesBackground key="particles" speed={0.4} particleCount={30} />]}
      >
        <div className="container-custom py-20 text-center">
          <ParallaxText speed={0.1}>
            <h2 className="text-4xl font-bold text-darkweb-text mb-8">
              Floating Particles
            </h2>
            <p className="text-darkweb-muted max-w-2xl mx-auto mb-12">
              Dynamic particle system with parallax movement creates an immersive experience
            </p>
          </ParallaxText>
          
          <div className="grid md:grid-cols-3 gap-8">
            {[1, 2, 3].map((item, index) => (
              <ParallaxContainer
                key={item}
                parallaxOptions={{ speed: 0.1 + (index * 0.05), direction: 'up' }}
                enableMouseParallax={true}
                mouseIntensity={0.03}
                animationDelay={index * 0.2}
              >
                <div className="bg-darkweb-card border border-darkweb-border rounded-lg p-6">
                  <div className="w-12 h-12 bg-darkweb-accent rounded-full mx-auto mb-4 flex items-center justify-center">
                    <span className="text-darkweb-bg font-bold">{item}</span>
                  </div>
                  <h3 className="text-xl font-bold text-darkweb-text mb-2">
                    Layer {item}
                  </h3>
                  <p className="text-darkweb-muted text-sm">
                    Different parallax speeds create depth perception
                  </p>
                </div>
              </ParallaxContainer>
            ))}
          </div>
        </div>
      </ContentParallaxSection>

      {/* Code Rain Section */}
      <ContentParallaxSection 
        className="min-h-screen bg-darkweb-surface/30"
        backgroundElements={[<CodeRainBackground key="code-rain" speed={0.6} intensity={0.05} />]}
      >
        <div className="container-custom py-20">
          <ParallaxText speed={0.2}>
            <h2 className="text-4xl font-bold text-darkweb-text mb-8 text-center">
              Matrix Code Rain
            </h2>
          </ParallaxText>
          
          <ParallaxContainer 
            parallaxOptions={{ speed: 0.3 }}
            enableMouseParallax={true}
            mouseIntensity={0.04}
          >
            <div className="max-w-4xl mx-auto bg-darkweb-card/80 backdrop-blur-sm border border-darkweb-border rounded-lg p-8">
              <div className="text-center space-y-6">
                <div className="text-darkweb-green font-mono text-lg">
                  {'> Accessing secure terminal...'}
                </div>
                <div className="text-darkweb-accent font-mono">
                  {'[AUTHENTICATED]'}
                </div>
                <p className="text-darkweb-text">
                  The code rain effect adds a cyberpunk aesthetic while maintaining smooth parallax performance.
                  Each column moves independently with different speeds and delays.
                </p>
              </div>
            </div>
          </ParallaxContainer>
        </div>
      </ContentParallaxSection>

      {/* Geometric Shapes Section */}
      <ContentParallaxSection 
        className="min-h-screen bg-gradient-to-t from-darkweb-bg to-darkweb-surface/20"
        backgroundElements={[<GeometricShapesBackground key="shapes" speed={0.2} shapeCount={12} />]}
      >
        <div className="container-custom py-20 text-center">
          <ParallaxText speed={0.15}>
            <h2 className="text-4xl font-bold text-darkweb-text mb-8">
              Geometric Parallax
            </h2>
            <p className="text-darkweb-muted max-w-2xl mx-auto mb-12">
              Floating geometric shapes with rotation and parallax create visual depth
            </p>
          </ParallaxText>
          
          <ParallaxContainer 
            parallaxOptions={{ speed: 0.25 }}
            enableMouseParallax={true}
            mouseIntensity={0.06}
          >
            <div className="bg-darkweb-card border border-darkweb-border rounded-lg p-8 max-w-2xl mx-auto">
              <h3 className="text-2xl font-bold text-darkweb-accent mb-4">
                Performance Optimized
              </h3>
              <p className="text-darkweb-text mb-6">
                All parallax effects use requestAnimationFrame and are throttled for optimal performance.
                GPU acceleration with transform3d ensures smooth animations.
              </p>
              <div className="flex justify-center gap-4">
                <span className="px-3 py-1 bg-darkweb-accent/20 text-darkweb-accent rounded text-sm">
                  60 FPS
                </span>
                <span className="px-3 py-1 bg-darkweb-green/20 text-darkweb-green rounded text-sm">
                  GPU Accelerated
                </span>
                <span className="px-3 py-1 bg-darkweb-purple/20 text-darkweb-purple rounded text-sm">
                  Mobile Optimized
                </span>
              </div>
            </div>
          </ParallaxContainer>
        </div>
      </ContentParallaxSection>
    </div>
  );
}
