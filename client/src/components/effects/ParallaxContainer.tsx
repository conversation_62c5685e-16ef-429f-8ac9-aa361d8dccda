'use client';

import React, { ReactNode } from 'react';
import { motion } from 'framer-motion';
import { useParallax, useScrollAnimation, useMouseParallax, ParallaxOptions } from '@/hooks/useParallax';

interface ParallaxContainerProps {
  children: ReactNode;
  className?: string;
  parallaxOptions?: ParallaxOptions;
  enableMouseParallax?: boolean;
  mouseIntensity?: number;
  animationDelay?: number;
  animationDuration?: number;
}

export function ParallaxContainer({
  children,
  className = '',
  parallaxOptions = {},
  enableMouseParallax = false,
  mouseIntensity = 0.05,
  animationDelay = 0,
  animationDuration = 0.8
}: ParallaxContainerProps) {
  const parallax = useParallax(parallaxOptions);
  const scrollAnimation = useScrollAnimation(0.1);
  const mouseParallax = useMouseParallax(enableMouseParallax ? mouseIntensity : 0);

  // Combine refs
  const combinedRef = (element: HTMLDivElement | null) => {
    if (element) {
      (parallax.ref as any).current = element;
      (scrollAnimation.ref as any).current = element;
      if (enableMouseParallax) {
        (mouseParallax.ref as any).current = element;
      }
    }
  };

  const combinedTransform = enableMouseParallax 
    ? `${parallax.transform} ${mouseParallax.transform}`
    : parallax.transform;

  return (
    <motion.div
      ref={combinedRef}
      className={className}
      style={{
        transform: combinedTransform,
        willChange: 'transform'
      }}
      initial={{ opacity: 0, y: 50 }}
      animate={scrollAnimation.isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
      transition={{
        duration: animationDuration,
        delay: animationDelay,
        ease: 'easeOut'
      }}
    >
      {children}
    </motion.div>
  );
}

// Specialized parallax components for different use cases
export function ParallaxBackground({
  children,
  className = '',
  speed = 0.3,
  direction = 'up' as const
}: {
  children: ReactNode;
  className?: string;
  speed?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
}) {
  return (
    <ParallaxContainer
      className={className}
      parallaxOptions={{ speed, direction }}
    >
      {children}
    </ParallaxContainer>
  );
}

export function ParallaxText({
  children,
  className = '',
  speed = 0.2,
  enableMouseParallax = true
}: {
  children: ReactNode;
  className?: string;
  speed?: number;
  enableMouseParallax?: boolean;
}) {
  return (
    <ParallaxContainer
      className={className}
      parallaxOptions={{ speed }}
      enableMouseParallax={enableMouseParallax}
      mouseIntensity={0.03}
    >
      {children}
    </ParallaxContainer>
  );
}

export function ParallaxImage({
  src,
  alt,
  className = '',
  speed = 0.4,
  direction = 'up' as const
}: {
  src: string;
  alt: string;
  className?: string;
  speed?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
}) {
  return (
    <ParallaxContainer
      className={className}
      parallaxOptions={{ speed, direction }}
    >
      <img src={src} alt={alt} className="w-full h-full object-cover" />
    </ParallaxContainer>
  );
}

// Floating elements with parallax
export function FloatingElement({
  children,
  className = '',
  speed = 0.6,
  direction = 'up' as const,
  delay = 0
}: {
  children: ReactNode;
  className?: string;
  speed?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
  delay?: number;
}) {
  return (
    <ParallaxContainer
      className={`absolute ${className}`}
      parallaxOptions={{ speed, direction }}
      enableMouseParallax={true}
      mouseIntensity={0.1}
      animationDelay={delay}
    >
      {children}
    </ParallaxContainer>
  );
}
