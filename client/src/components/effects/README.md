# Parallax Scroll Animation System

A comprehensive parallax scrolling system built with React, TypeScript, and Framer Motion for smooth, performant animations.

## Features

- 🚀 **Performance Optimized**: Uses `requestAnimationFrame` and GPU acceleration
- 📱 **Mobile Responsive**: Optimized for all device sizes
- 🎨 **Multiple Effects**: Various parallax backgrounds and animations
- 🖱️ **Mouse Parallax**: Interactive mouse-based parallax effects
- 🔧 **Highly Configurable**: Customizable speed, direction, and intensity
- ♿ **Accessible**: Respects user motion preferences

## Quick Start

### Basic Parallax Container

```tsx
import { ParallaxContainer } from '@/components/effects';

<ParallaxContainer
  parallaxOptions={{ speed: 0.5, direction: 'up' }}
  enableMouseParallax={true}
>
  <YourContent />
</ParallaxContainer>
```

### Parallax Sections

```tsx
import { ContentParallaxSection } from '@/components/effects';

<ContentParallaxSection 
  id="my-section"
  className="min-h-screen"
  backgroundElements={[<AnimatedGridBackground key="grid" />]}
>
  <YourSectionContent />
</ContentParallaxSection>
```

## Components

### Core Components

#### `ParallaxContainer`
Main wrapper for parallax effects.

**Props:**
- `parallaxOptions`: Configuration for scroll-based parallax
- `enableMouseParallax`: Enable mouse movement parallax
- `mouseIntensity`: Mouse parallax sensitivity (0-1)
- `animationDelay`: Delay before animation starts

#### `ParallaxSection`
Complete section wrapper with background effects and floating elements.

**Props:**
- `backgroundElements`: Array of background components
- `floatingElements`: Array of floating element configurations

### Specialized Components

#### `ParallaxText`
Optimized for text content with subtle parallax.

```tsx
<ParallaxText speed={0.1} enableMouseParallax={true}>
  <h1>Your Heading</h1>
</ParallaxText>
```

#### `ParallaxImage`
For images with parallax effects.

```tsx
<ParallaxImage 
  src="/path/to/image.jpg" 
  alt="Description"
  speed={0.4}
  direction="up"
/>
```

#### `FloatingElement`
For absolutely positioned floating elements.

```tsx
<FloatingElement 
  className="top-20 right-20"
  speed={0.6}
  direction="left"
  delay={0.5}
>
  <YourFloatingContent />
</FloatingElement>
```

### Background Effects

#### `AnimatedGridBackground`
Animated grid pattern background.

```tsx
<AnimatedGridBackground speed={0.3} opacity={0.1} />
```

#### `FloatingParticlesBackground`
Floating particle system.

```tsx
<FloatingParticlesBackground speed={0.4} particleCount={20} />
```

#### `CodeRainBackground`
Matrix-style code rain effect.

```tsx
<CodeRainBackground speed={0.6} intensity={0.05} />
```

#### `GeometricShapesBackground`
Floating geometric shapes.

```tsx
<GeometricShapesBackground speed={0.3} shapeCount={8} />
```

## Hooks

### `useParallax`
Core parallax hook for custom implementations.

```tsx
import { useParallax } from '@/hooks/useParallax';

const parallax = useParallax({
  speed: 0.5,
  direction: 'up',
  offset: 0,
  disabled: false
});

return (
  <div ref={parallax.ref} style={parallax.style}>
    Content
  </div>
);
```

### `useScrollAnimation`
Intersection Observer based scroll animations.

```tsx
import { useScrollAnimation } from '@/hooks/useParallax';

const { ref, isVisible } = useScrollAnimation(0.1);
```

### `useMouseParallax`
Mouse movement based parallax.

```tsx
import { useMouseParallax } from '@/hooks/useParallax';

const mouseParallax = useMouseParallax(0.1);
```

## Configuration Options

### ParallaxOptions

```typescript
interface ParallaxOptions {
  speed?: number;        // Parallax speed (0-1)
  direction?: 'up' | 'down' | 'left' | 'right';
  offset?: number;       // Initial offset
  disabled?: boolean;    // Disable parallax
}
```

## Performance Tips

1. **Use `transform3d`**: All animations use GPU acceleration
2. **Throttled Events**: Scroll events are throttled using `requestAnimationFrame`
3. **Intersection Observer**: Only animate visible elements
4. **Mobile Optimization**: Reduced effects on mobile devices

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Examples

Visit `/parallax-demo` to see all effects in action.

### Hero Section with Floating Elements

```tsx
<HeroParallaxSection>
  <ParallaxText speed={0.1}>
    <h1>Your Hero Title</h1>
  </ParallaxText>
</HeroParallaxSection>
```

### Content Section with Background

```tsx
<ContentParallaxSection 
  backgroundElements={[
    <AnimatedGridBackground key="grid" speed={0.3} />
  ]}
>
  <div className="container-custom">
    <ParallaxContainer parallaxOptions={{ speed: 0.2 }}>
      <YourContent />
    </ParallaxContainer>
  </div>
</ContentParallaxSection>
```

## Accessibility

The system respects the `prefers-reduced-motion` media query and can disable animations for users who prefer reduced motion.

## Contributing

When adding new parallax effects:
1. Ensure GPU acceleration with `transform3d`
2. Use `requestAnimationFrame` for smooth animations
3. Add proper TypeScript types
4. Test on mobile devices
5. Consider accessibility implications
