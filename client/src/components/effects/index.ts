// Parallax Hooks
export { useParallax, useScrollAnimation, useMouseParallax } from '@/hooks/useParallax';
export type { ParallaxOptions } from '@/hooks/useParallax';

// Parallax Components
export {
  ParallaxContainer,
  ParallaxBackground,
  ParallaxText,
  ParallaxImage,
  FloatingElement
} from './ParallaxContainer';

// Parallax Sections
export {
  ParallaxSection,
  HeroParallaxSection,
  ContentParallaxSection
} from './ParallaxSection';

// Parallax Backgrounds
export {
  ParallaxBackground as ParallaxBg,
  AnimatedGridBackground,
  FloatingParticlesBackground,
  CodeRainBackground,
  GeometricShapesBackground
} from './ParallaxBackground';

// Demo Component
export { ParallaxDemo } from './ParallaxDemo';

// Other Effects
export { CRTFlicker } from './CRTFlicker';
export { ScanlineOverlay } from './ScanlineOverlay';
export { ThreeBackground } from './ThreeBackground';
