'use client';

import { useEffect, useState, useRef } from 'react';

export interface ParallaxOptions {
  speed?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
  offset?: number;
  disabled?: boolean;
}

export function useParallax(options: ParallaxOptions = {}) {
  const {
    speed = 0.5,
    direction = 'up',
    offset = 0,
    disabled = false
  } = options;

  const [transform, setTransform] = useState('translate3d(0, 0, 0)');
  const elementRef = useRef<HTMLElement>(null);

  useEffect(() => {
    if (disabled || typeof window === 'undefined') return;

    const handleScroll = () => {
      if (!elementRef.current) return;

      const rect = elementRef.current.getBoundingClientRect();
      const scrolled = window.pageYOffset;
      const rate = scrolled * -speed;
      
      // Calculate the element's position relative to viewport
      const elementTop = rect.top + scrolled;
      const elementHeight = rect.height;
      const windowHeight = window.innerHeight;
      
      // Only apply parallax when element is in viewport
      const isInViewport = (
        elementTop < scrolled + windowHeight &&
        elementTop + elementHeight > scrolled
      );

      if (isInViewport) {
        let translateX = 0;
        let translateY = 0;

        switch (direction) {
          case 'up':
            translateY = rate + offset;
            break;
          case 'down':
            translateY = -rate + offset;
            break;
          case 'left':
            translateX = rate + offset;
            break;
          case 'right':
            translateX = -rate + offset;
            break;
        }

        setTransform(`translate3d(${translateX}px, ${translateY}px, 0)`);
      }
    };

    // Initial call
    handleScroll();

    // Throttled scroll listener for better performance
    let ticking = false;
    const throttledScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', throttledScroll, { passive: true });
    window.addEventListener('resize', handleScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', throttledScroll);
      window.removeEventListener('resize', handleScroll);
    };
  }, [speed, direction, offset, disabled]);

  return {
    ref: elementRef,
    transform,
    style: {
      transform,
      willChange: 'transform'
    }
  };
}

// Hook for scroll-triggered animations
export function useScrollAnimation(threshold = 0.1) {
  const [isVisible, setIsVisible] = useState(false);
  const elementRef = useRef<HTMLElement>(null);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting);
      },
      { threshold }
    );

    const currentElement = elementRef.current;
    if (currentElement) {
      observer.observe(currentElement);
    }

    return () => {
      if (currentElement) {
        observer.unobserve(currentElement);
      }
    };
  }, [threshold]);

  return {
    ref: elementRef,
    isVisible
  };
}

// Hook for mouse parallax effect
export function useMouseParallax(intensity = 0.1) {
  const [transform, setTransform] = useState('translate3d(0, 0, 0)');
  const elementRef = useRef<HTMLElement>(null);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleMouseMove = (e: MouseEvent) => {
      if (!elementRef.current) return;

      const rect = elementRef.current.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;
      
      const deltaX = (e.clientX - centerX) * intensity;
      const deltaY = (e.clientY - centerY) * intensity;

      setTransform(`translate3d(${deltaX}px, ${deltaY}px, 0)`);
    };

    window.addEventListener('mousemove', handleMouseMove, { passive: true });

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, [intensity]);

  return {
    ref: elementRef,
    transform,
    style: {
      transform,
      willChange: 'transform'
    }
  };
}
